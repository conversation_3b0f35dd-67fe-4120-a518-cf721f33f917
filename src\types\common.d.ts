export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message: string;
  errors?: Record<string, string[]>;
}

export interface PaginatedResponse<T> {
  data: T[];
  current_page: number;
  last_page: number;
  per_page: number;
  total: number;
}

export type AppView = 'login' | 'register' | 'profile' | 'dashboard';

export type ProfileTab = 'personal' | 'security' | 'preferences';

export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'tel' | 'url' | 'date' | 'textarea' | 'checkbox';
  required?: boolean;
  placeholder?: string;
  validation?: (value: any) => string | null;
}

export interface LoadingState {
  isLoading: boolean;
  error: string | null;
}
