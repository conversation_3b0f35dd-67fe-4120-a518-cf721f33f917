import { useState } from 'react'
import './App.css'
import RegisterForm from './auth/register'
import LoginForm from './auth/login'

type AuthView = 'login' | 'register'

function App() {
  const [currentView, setCurrentView] = useState<AuthView>('login')

  const switchToLogin = () => setCurrentView('login')
  const switchToRegister = () => setCurrentView('register')

  return (
    <div className="app-container">
      <div className="auth-container">
        {/* Navigation buttons */}
        <div className="auth-nav">
          <button
            onClick={switchToLogin}
            className={`nav-btn ${currentView === 'login' ? 'active' : ''}`}
          >
            Login
          </button>
          <button
            onClick={switchToRegister}
            className={`nav-btn ${currentView === 'register' ? 'active' : ''}`}
          >
            Register
          </button>
        </div>

        {/* Conditional rendering based on current view */}
        <div className="auth-form-container">
          {currentView === 'login' ? (
            <div>
              <h2>Welcome Back</h2>
              <p>Please sign in to your account</p>
              <LoginForm />
              <p className="auth-switch">
                Don't have an account?
                <button
                  type="button"
                  onClick={switchToRegister}
                  className="link-btn"
                >
                  Create one here
                </button>
              </p>
            </div>
          ) : (
            <div>
              <h2>Create Account</h2>
              <p>Please fill in the information below</p>
              <RegisterForm />
              <p className="auth-switch">
                Already have an account?
                <button
                  type="button"
                  onClick={switchToLogin}
                  className="link-btn"
                >
                  Sign in here
                </button>
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default App
