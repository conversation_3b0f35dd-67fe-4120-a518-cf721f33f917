import { useState } from 'react'
import './App.css'
import RegisterForm from './auth/register'
import LoginForm from './auth/login'
import ProfilePage from './profile/ProfilePage'

type AppView = 'login' | 'register' | 'profile'

function App() {
  const [currentView, setCurrentView] = useState<AppView>('login')
  const [isLoggedIn, setIsLoggedIn] = useState(false)

  const switchToLogin = () => setCurrentView('login')
  const switchToRegister = () => setCurrentView('register')
  const switchToProfile = () => setCurrentView('profile')

  const handleLogin = () => {
    setIsLoggedIn(true)
    setCurrentView('profile')
  }

  const handleLogout = () => {
    setIsLoggedIn(false)
    setCurrentView('login')
  }

  // If user is logged in and viewing profile, show profile page
  if (currentView === 'profile' && isLoggedIn) {
    return (
      <div>
        <nav className="main-nav">
          <div className="nav-content">
            <h1>My App</h1>
            <div className="nav-actions">
              <button onClick={switchToProfile} className="nav-link">
                Profile
              </button>
              <button onClick={handleLogout} className="logout-btn">
                Logout
              </button>
            </div>
          </div>
        </nav>
        <ProfilePage />
      </div>
    );
  }

  // Authentication pages
  return (
    <div className="app-container">
      <div className="auth-container">
        {/* Navigation buttons */}
        <div className="auth-nav">
          <button
            onClick={switchToLogin}
            className={`nav-btn ${currentView === 'login' ? 'active' : ''}`}
          >
            Login
          </button>
          <button
            onClick={switchToRegister}
            className={`nav-btn ${currentView === 'register' ? 'active' : ''}`}
          >
            Register
          </button>
        </div>

        {/* Conditional rendering based on current view */}
        <div className="auth-form-container">
          {currentView === 'login' ? (
            <div>
              <h2>Welcome Back</h2>
              <p>Please sign in to your account</p>
              <LoginForm onLoginSuccess={handleLogin} />
              <p className="auth-switch">
                Don't have an account?
                <button
                  type="button"
                  onClick={switchToRegister}
                  className="link-btn"
                >
                  Create one here
                </button>
              </p>
            </div>
          ) : (
            <div>
              <h2>Create Account</h2>
              <p>Please fill in the information below</p>
              <RegisterForm onRegisterSuccess={handleLogin} />
              <p className="auth-switch">
                Already have an account?
                <button
                  type="button"
                  onClick={switchToLogin}
                  className="link-btn"
                >
                  Sign in here
                </button>
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default App
