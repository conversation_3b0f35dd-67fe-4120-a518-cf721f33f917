.input-group {
  display: flex;
  flex-direction: column;
  margin-bottom: 1rem;
}

.input-group-full-width {
  grid-column: 1 / -1;
}

.input-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
}

.required-asterisk {
  color: #ef4444;
  margin-left: 0.25rem;
}

.input-field {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  transition: border-color 0.2s, box-shadow 0.2s;
  background: white;
}

.input-field:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.input-field:disabled {
  background: #f9fafb;
  color: #6b7280;
  cursor: not-allowed;
}

.input-field.input-error {
  border-color: #ef4444;
}

.input-field.input-error:focus {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.input-error-text {
  font-size: 0.75rem;
  color: #ef4444;
  margin-top: 0.25rem;
}

.input-helper-text {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.25rem;
}
