.dashboard-page {
  min-height: calc(100vh - 80px);
  background: #f8fafc;
  padding: 2rem 1rem;
}

.dashboard-container {
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-header {
  margin-bottom: 2rem;
}

.dashboard-header h1 {
  margin: 0 0 0.5rem 0;
  color: #111827;
  font-size: 2rem;
  font-weight: 700;
}

.dashboard-header p {
  margin: 0;
  color: #6b7280;
  font-size: 1.125rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  transition: transform 0.2s ease;
}

.stat-content {
  padding: 0.5rem;
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.stat-header h3 {
  margin: 0;
  color: #374151;
  font-size: 0.875rem;
  font-weight: 500;
}

.stat-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.stat-description {
  margin: 0;
  color: #6b7280;
  font-size: 0.875rem;
}

.dashboard-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

.dashboard-section h2 {
  margin: 0 0 1.5rem 0;
  color: #111827;
  font-size: 1.25rem;
  font-weight: 600;
}

.quick-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.activity-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.activity-content h4 {
  margin: 0 0 0.25rem 0;
  color: #111827;
  font-size: 0.875rem;
  font-weight: 600;
}

.activity-content p {
  margin: 0;
  color: #6b7280;
  font-size: 0.75rem;
}

.activity-time {
  color: #9ca3af;
  font-size: 0.75rem;
  white-space: nowrap;
  margin-left: 1rem;
}

.account-overview {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.overview-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid #e5e7eb;
}

.overview-item:last-child {
  border-bottom: none;
}

.overview-label {
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
}

.overview-value {
  color: #111827;
  font-size: 0.875rem;
  font-weight: 600;
  text-align: right;
}

@media (max-width: 768px) {
  .dashboard-header h1 {
    font-size: 1.5rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .dashboard-content {
    grid-template-columns: 1fr;
  }

  .quick-actions {
    grid-template-columns: 1fr;
  }

  .activity-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .activity-time {
    margin-left: 0;
  }

  .overview-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .overview-value {
    text-align: left;
  }
}
