import React from 'react';
import { useAuth } from '../../context/AuthContext';
import { Card } from '../../components/common/Card';
import { Button } from '../../components/forms/Button';
import { formatDate } from '../../utils/formatters';
import './DashboardPage.css';

const DashboardPage: React.FC = () => {
  const { user } = useAuth();

  if (!user) return null;

  const stats = [
    {
      title: 'Profile Completion',
      value: '85%',
      description: 'Complete your profile to unlock all features',
      color: '#3b82f6'
    },
    {
      title: 'Account Status',
      value: 'Active',
      description: 'Your account is in good standing',
      color: '#10b981'
    },
    {
      title: 'Last Login',
      value: 'Today',
      description: 'Keep your account secure',
      color: '#f59e0b'
    },
    {
      title: 'Security Score',
      value: '7/10',
      description: 'Enable 2FA to improve security',
      color: '#ef4444'
    }
  ];

  const recentActivity = [
    {
      action: 'Profile Updated',
      timestamp: new Date().toISOString(),
      description: 'You updated your profile information'
    },
    {
      action: 'Login',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      description: 'Successful login from your device'
    },
    {
      action: 'Password Changed',
      timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
      description: 'You changed your account password'
    }
  ];

  return (
    <div className="dashboard-page">
      <div className="dashboard-container">
        <div className="dashboard-header">
          <h1>Welcome back, {user.firstName}!</h1>
          <p>Here's what's happening with your account today.</p>
        </div>

        <div className="stats-grid">
          {stats.map((stat, index) => (
            <Card key={index} className="stat-card" hover>
              <div className="stat-content">
                <div className="stat-header">
                  <h3>{stat.title}</h3>
                  <div 
                    className="stat-indicator"
                    style={{ backgroundColor: stat.color }}
                  ></div>
                </div>
                <div className="stat-value" style={{ color: stat.color }}>
                  {stat.value}
                </div>
                <p className="stat-description">{stat.description}</p>
              </div>
            </Card>
          ))}
        </div>

        <div className="dashboard-content">
          <div className="dashboard-section">
            <Card>
              <h2>Quick Actions</h2>
              <div className="quick-actions">
                <Button variant="primary">
                  Update Profile
                </Button>
                <Button variant="outline">
                  Change Password
                </Button>
                <Button variant="outline">
                  Security Settings
                </Button>
                <Button variant="outline">
                  Download Data
                </Button>
              </div>
            </Card>
          </div>

          <div className="dashboard-section">
            <Card>
              <h2>Recent Activity</h2>
              <div className="activity-list">
                {recentActivity.map((activity, index) => (
                  <div key={index} className="activity-item">
                    <div className="activity-content">
                      <h4>{activity.action}</h4>
                      <p>{activity.description}</p>
                    </div>
                    <div className="activity-time">
                      {formatDate(activity.timestamp, {
                        month: 'short',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          </div>

          <div className="dashboard-section">
            <Card>
              <h2>Account Overview</h2>
              <div className="account-overview">
                <div className="overview-item">
                  <span className="overview-label">Member Since</span>
                  <span className="overview-value">
                    {formatDate(user.createdAt)}
                  </span>
                </div>
                <div className="overview-item">
                  <span className="overview-label">Email</span>
                  <span className="overview-value">{user.email}</span>
                </div>
                <div className="overview-item">
                  <span className="overview-label">Location</span>
                  <span className="overview-value">
                    {user.location || 'Not specified'}
                  </span>
                </div>
                <div className="overview-item">
                  <span className="overview-label">Phone</span>
                  <span className="overview-value">
                    {user.phone || 'Not specified'}
                  </span>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardPage;
