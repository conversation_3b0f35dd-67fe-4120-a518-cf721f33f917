import { useState } from "react";
import "../index.css";

interface RegisterFormProps {
  onRegisterSuccess?: () => void;
}

export default function RegisterForm({ onRegisterSuccess }: RegisterFormProps) {
  const [formData, setFormData] = useState({
    email: "",
    password: "",
    repeatPassword: "",
    terms: false,
  });

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const { id, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [id]: type === "checkbox" ? checked : value,
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate passwords match
    if (formData.password !== formData.repeatPassword) {
      alert('Passwords do not match!');
      return;
    }

    console.log("Form submitted:", formData);
    // You can send data to Laravel API here

    // Simulate successful registration
    if (formData.email && formData.password && formData.terms) {
      onRegisterSuccess?.();
    }
  };

  return (
    <form onSubmit={handleSubmit} className="form">
      <div className="form-group">
        <label htmlFor="email">Your email</label>
        <input
          type="email"
          id="email"
          value={formData.email}
          onChange={handleChange}
          placeholder="<EMAIL>"
          required
        />
      </div>

      <div className="form-group">
        <label htmlFor="password">Your password</label>
        <input
          type="password"
          id="password"
          value={formData.password}
          onChange={handleChange}
          required
        />
      </div>

      <div className="form-group">
        <label htmlFor="repeatPassword">Repeat password</label>
        <input
          type="password"
          id="repeatPassword"
          value={formData.repeatPassword}
          onChange={handleChange}
          required
        />
      </div>

      <div className="form-checkbox">
        <input
          type="checkbox"
          id="terms"
          checked={formData.terms}
          onChange={handleChange}
          required
        />
        <label htmlFor="terms">
          I agree with the <a href="#">terms and conditions</a>
        </label>
      </div>

      <button type="submit" className="btn">
        Register new account
      </button>
    </form>
  );
}
