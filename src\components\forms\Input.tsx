import React from 'react';
import './Input.css';

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  helperText?: string;
  fullWidth?: boolean;
}

export const Input: React.FC<InputProps> = ({
  label,
  error,
  helperText,
  fullWidth = false,
  className = '',
  ...props
}) => {
  const inputClasses = [
    'input-field',
    fullWidth ? 'input-full-width' : '',
    error ? 'input-error' : '',
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={`input-group ${fullWidth ? 'input-group-full-width' : ''}`}>
      {label && (
        <label htmlFor={props.id} className="input-label">
          {label}
          {props.required && <span className="required-asterisk">*</span>}
        </label>
      )}
      <input
        className={inputClasses}
        {...props}
      />
      {error && <span className="input-error-text">{error}</span>}
      {helperText && !error && <span className="input-helper-text">{helperText}</span>}
    </div>
  );
};
