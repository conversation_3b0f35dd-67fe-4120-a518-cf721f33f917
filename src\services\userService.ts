import { User, UserProfile, SecuritySettings } from '../types/user';
import { ApiResponse } from '../types/common';
import authService from './authService';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api';

class UserService {
  private getAuthHeaders() {
    const token = authService.getToken();
    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    };
  }

  async getProfile(): Promise<User> {
    try {
      const response = await fetch(`${API_BASE_URL}/user/profile`, {
        method: 'GET',
        headers: this.getAuthHeaders(),
      });

      const data: ApiResponse<User> = await response.json();

      if (data.success && data.data) {
        return data.data;
      } else {
        throw new Error(data.message || 'Failed to fetch profile');
      }
    } catch (error) {
      console.error('Get profile error:', error);
      throw error;
    }
  }

  async updateProfile(profileData: Partial<UserProfile>): Promise<User> {
    try {
      const response = await fetch(`${API_BASE_URL}/user/profile`, {
        method: 'PUT',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(profileData),
      });

      const data: ApiResponse<User> = await response.json();

      if (data.success && data.data) {
        // Update local storage
        localStorage.setItem('user', JSON.stringify(data.data));
        return data.data;
      } else {
        throw new Error(data.message || 'Failed to update profile');
      }
    } catch (error) {
      console.error('Update profile error:', error);
      throw error;
    }
  }

  async changePassword(passwordData: {
    currentPassword: string;
    newPassword: string;
    confirmPassword: string;
  }): Promise<void> {
    try {
      const response = await fetch(`${API_BASE_URL}/user/change-password`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(passwordData),
      });

      const data: ApiResponse = await response.json();

      if (!data.success) {
        throw new Error(data.message || 'Failed to change password');
      }
    } catch (error) {
      console.error('Change password error:', error);
      throw error;
    }
  }

  async updateSecuritySettings(settings: Partial<SecuritySettings>): Promise<void> {
    try {
      const response = await fetch(`${API_BASE_URL}/user/security-settings`, {
        method: 'PUT',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(settings),
      });

      const data: ApiResponse = await response.json();

      if (!data.success) {
        throw new Error(data.message || 'Failed to update security settings');
      }
    } catch (error) {
      console.error('Update security settings error:', error);
      throw error;
    }
  }

  async uploadProfileImage(file: File): Promise<string> {
    try {
      const formData = new FormData();
      formData.append('profile_image', file);

      const token = authService.getToken();
      const response = await fetch(`${API_BASE_URL}/user/profile-image`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        body: formData,
      });

      const data: ApiResponse<{ imageUrl: string }> = await response.json();

      if (data.success && data.data) {
        return data.data.imageUrl;
      } else {
        throw new Error(data.message || 'Failed to upload image');
      }
    } catch (error) {
      console.error('Upload image error:', error);
      throw error;
    }
  }

  async deleteAccount(): Promise<void> {
    try {
      const response = await fetch(`${API_BASE_URL}/user/delete-account`, {
        method: 'DELETE',
        headers: this.getAuthHeaders(),
      });

      const data: ApiResponse = await response.json();

      if (data.success) {
        // Clear local storage
        authService.logout();
      } else {
        throw new Error(data.message || 'Failed to delete account');
      }
    } catch (error) {
      console.error('Delete account error:', error);
      throw error;
    }
  }

  // Mock methods for development
  async mockUpdateProfile(profileData: Partial<UserProfile>): Promise<User> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const currentUser = authService.getCurrentUser();
        if (currentUser) {
          const updatedUser: User = {
            ...currentUser,
            ...profileData,
            updatedAt: new Date().toISOString(),
          };
          localStorage.setItem('user', JSON.stringify(updatedUser));
          resolve(updatedUser);
        }
      }, 500);
    });
  }

  async mockChangePassword(): Promise<void> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve();
      }, 500);
    });
  }
}

export default new UserService();
