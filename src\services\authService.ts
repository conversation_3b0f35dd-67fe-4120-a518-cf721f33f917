import { LoginCredentials, RegisterData, AuthResponse, User } from '../types/user';
import { ApiResponse } from '../types/common';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api';

class AuthService {
  private token: string | null = null;

  constructor() {
    this.token = localStorage.getItem('auth_token');
  }

  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      });

      const data: ApiResponse<AuthResponse> = await response.json();

      if (data.success && data.data) {
        this.token = data.data.token;
        localStorage.setItem('auth_token', this.token);
        localStorage.setItem('user', JSON.stringify(data.data.user));
        return data.data;
      } else {
        throw new Error(data.message || 'Login failed');
      }
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }

  async register(userData: RegisterData): Promise<AuthResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      });

      const data: ApiResponse<AuthResponse> = await response.json();

      if (data.success && data.data) {
        this.token = data.data.token;
        localStorage.setItem('auth_token', this.token);
        localStorage.setItem('user', JSON.stringify(data.data.user));
        return data.data;
      } else {
        throw new Error(data.message || 'Registration failed');
      }
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    }
  }

  async logout(): Promise<void> {
    try {
      if (this.token) {
        await fetch(`${API_BASE_URL}/auth/logout`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.token}`,
            'Content-Type': 'application/json',
          },
        });
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      this.token = null;
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user');
    }
  }

  async refreshToken(): Promise<string | null> {
    try {
      if (!this.token) return null;

      const response = await fetch(`${API_BASE_URL}/auth/refresh`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.token}`,
          'Content-Type': 'application/json',
        },
      });

      const data: ApiResponse<{ token: string }> = await response.json();

      if (data.success && data.data) {
        this.token = data.data.token;
        localStorage.setItem('auth_token', this.token);
        return this.token;
      }

      return null;
    } catch (error) {
      console.error('Token refresh error:', error);
      return null;
    }
  }

  getToken(): string | null {
    return this.token;
  }

  getCurrentUser(): User | null {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  }

  isAuthenticated(): boolean {
    return !!this.token && !!this.getCurrentUser();
  }

  // Mock login for development
  async mockLogin(credentials: LoginCredentials): Promise<AuthResponse> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const mockUser: User = {
          id: '1',
          firstName: 'njewa',
          lastName: 'chifundo',
          email: credentials.email,
          phone: '+265 123 456 789',
          bio: 'Software developer passionate about creating amazing user experiences.',
          dateOfBirth: '1990-01-15',
          location: 'mtandile, lilongwe',
          website: 'myport-e2eb.vercel.app',
          profileImage: '',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };

        const authResponse: AuthResponse = {
          user: mockUser,
          token: 'mock-jwt-token-' + Date.now(),
          message: 'Login successful'
        };

        this.token = authResponse.token;
        localStorage.setItem('auth_token', this.token);
        localStorage.setItem('user', JSON.stringify(mockUser));

        resolve(authResponse);
      }, 1000);
    });
  }

  // Mock register for development
  async mockRegister(userData: RegisterData): Promise<AuthResponse> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const mockUser: User = {
          id: '1',
          firstName: userData.firstName || 'New',
          lastName: userData.lastName || 'User',
          email: userData.email,
          phone: '',
          bio: '',
          dateOfBirth: '',
          location: '',
          website: '',
          profileImage: '',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };

        const authResponse: AuthResponse = {
          user: mockUser,
          token: 'mock-jwt-token-' + Date.now(),
          message: 'Registration successful'
        };

        this.token = authResponse.token;
        localStorage.setItem('auth_token', this.token);
        localStorage.setItem('user', JSON.stringify(mockUser));

        resolve(authResponse);
      }, 1000);
    });
  }
}

export default new AuthService();
