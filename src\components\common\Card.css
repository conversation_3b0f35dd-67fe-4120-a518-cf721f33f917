.card {
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
}

.card-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Padding variants */
.card-padding-none {
  padding: 0;
}

.card-padding-small {
  padding: 1rem;
}

.card-padding-medium {
  padding: 1.5rem;
}

.card-padding-large {
  padding: 2rem;
}

/* Shadow variants */
.card-shadow-none {
  box-shadow: none;
}

.card-shadow-small {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.card-shadow-medium {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card-shadow-large {
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
}
