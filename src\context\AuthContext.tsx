import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { User, LoginCredentials, RegisterData } from '../types/user';
import { LoadingState } from '../types/common';
import authService from '../services/authService';

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  loading: LoadingState;
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (userData: RegisterData) => Promise<void>;
  logout: () => Promise<void>;
  updateUser: (userData: Partial<User>) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState<LoadingState>({
    isLoading: true,
    error: null
  });

  useEffect(() => {
    // Check if user is already logged in on app start
    const initializeAuth = () => {
      try {
        const currentUser = authService.getCurrentUser();
        const token = authService.getToken();
        
        if (currentUser && token) {
          setUser(currentUser);
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        setLoading(prev => ({ ...prev, error: 'Failed to initialize authentication' }));
      } finally {
        setLoading(prev => ({ ...prev, isLoading: false }));
      }
    };

    initializeAuth();
  }, []);

  const login = async (credentials: LoginCredentials): Promise<void> => {
    setLoading({ isLoading: true, error: null });
    
    try {
      // Use mock login for development
      const authResponse = await authService.mockLogin(credentials);
      setUser(authResponse.user);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Login failed';
      setLoading(prev => ({ ...prev, error: errorMessage }));
      throw error;
    } finally {
      setLoading(prev => ({ ...prev, isLoading: false }));
    }
  };

  const register = async (userData: RegisterData): Promise<void> => {
    setLoading({ isLoading: true, error: null });
    
    try {
      // Use mock register for development
      const authResponse = await authService.mockRegister(userData);
      setUser(authResponse.user);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Registration failed';
      setLoading(prev => ({ ...prev, error: errorMessage }));
      throw error;
    } finally {
      setLoading(prev => ({ ...prev, isLoading: false }));
    }
  };

  const logout = async (): Promise<void> => {
    setLoading({ isLoading: true, error: null });
    
    try {
      await authService.logout();
      setUser(null);
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setLoading(prev => ({ ...prev, isLoading: false }));
    }
  };

  const updateUser = (userData: Partial<User>): void => {
    if (user) {
      const updatedUser = { ...user, ...userData };
      setUser(updatedUser);
      localStorage.setItem('user', JSON.stringify(updatedUser));
    }
  };

  const value: AuthContextType = {
    user,
    isAuthenticated: !!user,
    loading,
    login,
    register,
    logout,
    updateUser
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
