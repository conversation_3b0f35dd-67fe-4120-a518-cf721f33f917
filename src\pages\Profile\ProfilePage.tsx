import React, { useState, useEffect } from 'react';
import { useAuth } from '../../context/AuthContext';
import { Card } from '../../components/common/Card';
import { Button } from '../../components/forms/Button';
import { Input } from '../../components/forms/Input';
import { formatDate, formatPhone, formatUrl } from '../../utils/formatters';
import { validateEmail, validatePhone, validateUrl } from '../../utils/validation';
import { UserProfile, SecuritySettings, ProfileTab } from '../../types/user';
import userService from '../../services/userService';
import './ProfilePage.css';

const ProfilePage: React.FC = () => {
  const { user, updateUser } = useAuth();
  const [activeTab, setActiveTab] = useState<ProfileTab>('personal');
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  
  const [profileData, setProfileData] = useState<UserProfile>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    bio: '',
    dateOfBirth: '',
    location: '',
    website: '',
    profileImage: ''
  });

  const [securityData, setSecurityData] = useState<SecuritySettings>({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
    twoFactorEnabled: false,
    emailNotifications: true,
    smsNotifications: false
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (user) {
      setProfileData({
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        phone: user.phone || '',
        bio: user.bio || '',
        dateOfBirth: user.dateOfBirth || '',
        location: user.location || '',
        website: user.website || '',
        profileImage: user.profileImage || ''
      });
    }
  }, [user]);

  const handleProfileChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setProfileData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleSecurityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setSecurityData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const validateProfileForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    
    if (!profileData.firstName.trim()) newErrors.firstName = 'First name is required';
    if (!profileData.lastName.trim()) newErrors.lastName = 'Last name is required';
    
    const emailError = validateEmail(profileData.email);
    if (emailError) newErrors.email = emailError;
    
    const phoneError = validatePhone(profileData.phone);
    if (phoneError) newErrors.phone = phoneError;
    
    const urlError = validateUrl(profileData.website);
    if (urlError) newErrors.website = urlError;
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleProfileSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateProfileForm()) return;
    
    setLoading(true);
    try {
      // Use mock service for development
      const updatedUser = await userService.mockUpdateProfile(profileData);
      updateUser(updatedUser);
      setIsEditing(false);
    } catch (error) {
      console.error('Profile update failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSecuritySubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (securityData.newPassword !== securityData.confirmPassword) {
      setErrors({ confirmPassword: 'Passwords do not match' });
      return;
    }
    
    setLoading(true);
    try {
      await userService.mockChangePassword();
      setSecurityData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
        twoFactorEnabled: securityData.twoFactorEnabled,
        emailNotifications: securityData.emailNotifications,
        smsNotifications: securityData.smsNotifications
      });
    } catch (error) {
      console.error('Password change failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (event) => {
        setProfileData(prev => ({
          ...prev,
          profileImage: event.target?.result as string
        }));
      };
      reader.readAsDataURL(file);
    }
  };

  if (!user) return null;

  return (
    <div className="profile-page">
      <div className="profile-container">
        <Card className="profile-header-card">
          <div className="profile-header">
            <div className="profile-avatar">
              {profileData.profileImage ? (
                <img src={profileData.profileImage} alt="Profile" />
              ) : (
                <div className="avatar-placeholder">
                  {profileData.firstName.charAt(0)}{profileData.lastName.charAt(0)}
                </div>
              )}
              {isEditing && (
                <label className="avatar-upload">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    style={{ display: 'none' }}
                  />
                  <span>Change Photo</span>
                </label>
              )}
            </div>
            <div className="profile-info">
              <h1>{profileData.firstName} {profileData.lastName}</h1>
              <p>{profileData.email}</p>
            </div>
          </div>
        </Card>

        <Card className="profile-tabs-card">
          <div className="profile-tabs">
            <button
              onClick={() => setActiveTab('personal')}
              className={`tab-btn ${activeTab === 'personal' ? 'active' : ''}`}
            >
              Personal Info
            </button>
            <button
              onClick={() => setActiveTab('security')}
              className={`tab-btn ${activeTab === 'security' ? 'active' : ''}`}
            >
              Security
            </button>
            <button
              onClick={() => setActiveTab('preferences')}
              className={`tab-btn ${activeTab === 'preferences' ? 'active' : ''}`}
            >
              Preferences
            </button>
          </div>
        </Card>

        <Card className="profile-content-card">
          {activeTab === 'personal' && (
            <div className="tab-content">
              <div className="section-header">
                <h2>Personal Information</h2>
                <Button
                  variant="outline"
                  onClick={() => setIsEditing(!isEditing)}
                >
                  {isEditing ? 'Cancel' : 'Edit'}
                </Button>
              </div>

              {!isEditing ? (
                <div className="profile-display">
                  <div className="info-grid">
                    <div className="info-item">
                      <span className="info-label">First Name</span>
                      <span className="info-value">{profileData.firstName}</span>
                    </div>

                    <div className="info-item">
                      <span className="info-label">Last Name</span>
                      <span className="info-value">{profileData.lastName}</span>
                    </div>

                    <div className="info-item">
                      <span className="info-label">Email</span>
                      <span className="info-value">{profileData.email}</span>
                    </div>

                    <div className="info-item">
                      <span className="info-label">Phone</span>
                      <span className="info-value">{formatPhone(profileData.phone)}</span>
                    </div>

                    <div className="info-item">
                      <span className="info-label">Date of Birth</span>
                      <span className="info-value">{formatDate(profileData.dateOfBirth)}</span>
                    </div>

                    <div className="info-item">
                      <span className="info-label">Location</span>
                      <span className="info-value">{profileData.location || 'Not provided'}</span>
                    </div>

                    <div className="info-item full-width">
                      <span className="info-label">Website</span>
                      <span className="info-value">
                        {profileData.website ? (
                          <a href={formatUrl(profileData.website)} target="_blank" rel="noopener noreferrer">
                            {profileData.website}
                          </a>
                        ) : 'Not provided'}
                      </span>
                    </div>

                    <div className="info-item full-width">
                      <span className="info-label">Bio</span>
                      <span className="info-value bio-text">
                        {profileData.bio || 'No bio provided'}
                      </span>
                    </div>
                  </div>
                </div>
              ) : (
                <form onSubmit={handleProfileSubmit}>
                  <div className="form-grid">
                    <Input
                      name="firstName"
                      label="First Name"
                      value={profileData.firstName}
                      onChange={handleProfileChange}
                      error={errors.firstName}
                      required
                    />

                    <Input
                      name="lastName"
                      label="Last Name"
                      value={profileData.lastName}
                      onChange={handleProfileChange}
                      error={errors.lastName}
                      required
                    />

                    <Input
                      type="email"
                      name="email"
                      label="Email"
                      value={profileData.email}
                      onChange={handleProfileChange}
                      error={errors.email}
                      required
                    />

                    <Input
                      type="tel"
                      name="phone"
                      label="Phone"
                      value={profileData.phone}
                      onChange={handleProfileChange}
                      error={errors.phone}
                      placeholder="+****************"
                    />

                    <Input
                      type="date"
                      name="dateOfBirth"
                      label="Date of Birth"
                      value={profileData.dateOfBirth}
                      onChange={handleProfileChange}
                    />

                    <Input
                      name="location"
                      label="Location"
                      value={profileData.location}
                      onChange={handleProfileChange}
                      placeholder="City, Country"
                    />

                    <Input
                      type="url"
                      name="website"
                      label="Website"
                      value={profileData.website}
                      onChange={handleProfileChange}
                      error={errors.website}
                      placeholder="https://yourwebsite.com"
                      fullWidth
                    />

                    <div className="form-group full-width">
                      <label htmlFor="bio" className="input-label">Bio</label>
                      <textarea
                        id="bio"
                        name="bio"
                        value={profileData.bio}
                        onChange={handleProfileChange}
                        rows={4}
                        placeholder="Tell us about yourself..."
                        className="input-field"
                      />
                    </div>
                  </div>

                  <div className="form-actions">
                    <Button type="submit" loading={loading}>
                      Save Changes
                    </Button>
                    <Button
                      type="button"
                      variant="secondary"
                      onClick={() => setIsEditing(false)}
                    >
                      Cancel
                    </Button>
                  </div>
                </form>
              )}
            </div>
          )}

          {activeTab === 'security' && (
            <div className="tab-content">
              <h2>Security Settings</h2>
              
              <form onSubmit={handleSecuritySubmit}>
                <div className="security-section">
                  <h3>Change Password</h3>
                  <div className="form-grid">
                    <Input
                      type="password"
                      name="currentPassword"
                      label="Current Password"
                      value={securityData.currentPassword}
                      onChange={handleSecurityChange}
                      fullWidth
                    />

                    <Input
                      type="password"
                      name="newPassword"
                      label="New Password"
                      value={securityData.newPassword}
                      onChange={handleSecurityChange}
                      fullWidth
                    />

                    <Input
                      type="password"
                      name="confirmPassword"
                      label="Confirm New Password"
                      value={securityData.confirmPassword}
                      onChange={handleSecurityChange}
                      error={errors.confirmPassword}
                      fullWidth
                    />
                  </div>

                  <Button type="submit" loading={loading}>
                    Update Password
                  </Button>
                </div>
              </form>

              <div className="security-section">
                <h3>Security Options</h3>
                <div className="security-option">
                  <label className="switch-label">
                    <input
                      type="checkbox"
                      name="twoFactorEnabled"
                      checked={securityData.twoFactorEnabled}
                      onChange={handleSecurityChange}
                    />
                    <span className="switch"></span>
                    Enable Two-Factor Authentication
                  </label>
                  <p>Add an extra layer of security to your account</p>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'preferences' && (
            <div className="tab-content">
              <h2>Notification Preferences</h2>
              
              <div className="preferences-section">
                <div className="preference-option">
                  <label className="switch-label">
                    <input
                      type="checkbox"
                      name="emailNotifications"
                      checked={securityData.emailNotifications}
                      onChange={handleSecurityChange}
                    />
                    <span className="switch"></span>
                    Email Notifications
                  </label>
                  <p>Receive updates and notifications via email</p>
                </div>

                <div className="preference-option">
                  <label className="switch-label">
                    <input
                      type="checkbox"
                      name="smsNotifications"
                      checked={securityData.smsNotifications}
                      onChange={handleSecurityChange}
                    />
                    <span className="switch"></span>
                    SMS Notifications
                  </label>
                  <p>Receive important alerts via text message</p>
                </div>
              </div>

              <div className="danger-zone">
                <h3>Danger Zone</h3>
                <Button variant="danger">
                  Delete Account
                </Button>
                <p>Once you delete your account, there is no going back. Please be certain.</p>
              </div>
            </div>
          )}
        </Card>
      </div>
    </div>
  );
};

export default ProfilePage;
