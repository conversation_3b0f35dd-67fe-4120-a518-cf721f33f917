import React, { useState } from 'react';
import './ProfilePage.css';

interface UserProfile {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  bio: string;
  dateOfBirth: string;
  location: string;
  website: string;
  profileImage: string;
}

interface SecuritySettings {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
  twoFactorEnabled: boolean;
  emailNotifications: boolean;
  smsNotifications: boolean;
}

type ProfileTab = 'personal' | 'security' | 'preferences';

export default function ProfilePage() {
  const [activeTab, setActiveTab] = useState<ProfileTab>('personal');
  const [isEditing, setIsEditing] = useState(false);
  const [profileData, setProfileData] = useState<UserProfile>({
    firstName: 'njewa',
    lastName: 'chifundo',
    email: '<EMAIL>',
    phone: '+****************',
    bio: 'Software developer passionate about creating amazing user experiences.',
    dateOfBirth: '1990-01-15',
    location: 'mtandile, lilongwe',
    website: 'myport-e2eb.vercel.app',
    profileImage: ''
  });

  const [securityData, setSecurityData] = useState<SecuritySettings>({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
    twoFactorEnabled: false,
    emailNotifications: true,
    smsNotifications: false
  });

  const handleProfileChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setProfileData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSecurityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setSecurityData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleProfileSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Profile updated:', profileData);
    setIsEditing(false);
    // Send to Laravel API
  };

  const handleSecuritySubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (securityData.newPassword !== securityData.confirmPassword) {
      alert('Passwords do not match!');
      return;
    }
    console.log('Security settings updated:', securityData);
    // Send to Laravel API
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (event) => {
        setProfileData(prev => ({
          ...prev,
          profileImage: event.target?.result as string
        }));
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <div className="profile-page">
      <div className="profile-container">
        <div className="profile-header">
          <div className="profile-avatar">
            {profileData.profileImage ? (
              <img src={profileData.profileImage} alt="Profile" />
            ) : (
              <div className="avatar-placeholder">
                {profileData.firstName.charAt(0)}{profileData.lastName.charAt(0)}
              </div>
            )}
            {isEditing && (
              <label className="avatar-upload">
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  style={{ display: 'none' }}
                />
                <span>Change Photo</span>
              </label>
            )}
          </div>
          <div className="profile-info">
            <h1>{profileData.firstName} {profileData.lastName}</h1>
            <p>{profileData.email}</p>
          </div>
        </div>

        <div className="profile-tabs">
          <button
            onClick={() => setActiveTab('personal')}
            className={`tab-btn ${activeTab === 'personal' ? 'active' : ''}`}
          >
            Personal Info
          </button>
          <button
            onClick={() => setActiveTab('security')}
            className={`tab-btn ${activeTab === 'security' ? 'active' : ''}`}
          >
            Security
          </button>
          <button
            onClick={() => setActiveTab('preferences')}
            className={`tab-btn ${activeTab === 'preferences' ? 'active' : ''}`}
          >
            Preferences
          </button>
        </div>

        <div className="profile-content">
          {activeTab === 'personal' && (
            <div className="tab-content">
              <div className="section-header">
                <h2>Personal Information</h2>
                <button
                  onClick={() => setIsEditing(!isEditing)}
                  className="edit-btn"
                >
                  {isEditing ? 'Cancel' : 'Edit'}
                </button>
              </div>

              {!isEditing ? (
                // Display Mode - Show information in a clean format
                <div className="profile-display">
                  <div className="info-grid">
                    <div className="info-item">
                      <span className="info-label">First Name</span>
                      <span className="info-value">{profileData.firstName}</span>
                    </div>

                    <div className="info-item">
                      <span className="info-label">Last Name</span>
                      <span className="info-value">{profileData.lastName}</span>
                    </div>

                    <div className="info-item">
                      <span className="info-label">Email</span>
                      <span className="info-value">{profileData.email}</span>
                    </div>

                    <div className="info-item">
                      <span className="info-label">Phone</span>
                      <span className="info-value">{profileData.phone || 'Not provided'}</span>
                    </div>

                    <div className="info-item">
                      <span className="info-label">Date of Birth</span>
                      <span className="info-value">
                        {profileData.dateOfBirth ?
                          new Date(profileData.dateOfBirth).toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric'
                          }) : 'Not provided'
                        }
                      </span>
                    </div>

                    <div className="info-item">
                      <span className="info-label">Location</span>
                      <span className="info-value">{profileData.location || 'Not provided'}</span>
                    </div>

                    <div className="info-item full-width">
                      <span className="info-label">Website</span>
                      <span className="info-value">
                        {profileData.website ? (
                          <a href={profileData.website} target="_blank" rel="noopener noreferrer">
                            {profileData.website}
                          </a>
                        ) : 'Not provided'}
                      </span>
                    </div>

                    <div className="info-item full-width">
                      <span className="info-label">Bio</span>
                      <span className="info-value bio-text">
                        {profileData.bio || 'No bio provided'}
                      </span>
                    </div>
                  </div>
                </div>
              ) : (
                // Edit Mode - Show form fields
                <form onSubmit={handleProfileSubmit}>
                  <div className="form-grid">
                    <div className="form-group">
                      <label htmlFor="firstName">First Name</label>
                      <input
                        type="text"
                        id="firstName"
                        name="firstName"
                        value={profileData.firstName}
                        onChange={handleProfileChange}
                        required
                      />
                    </div>

                    <div className="form-group">
                      <label htmlFor="lastName">Last Name</label>
                      <input
                        type="text"
                        id="lastName"
                        name="lastName"
                        value={profileData.lastName}
                        onChange={handleProfileChange}
                        required
                      />
                    </div>

                    <div className="form-group">
                      <label htmlFor="email">Email</label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={profileData.email}
                        onChange={handleProfileChange}
                        required
                      />
                    </div>

                    <div className="form-group">
                      <label htmlFor="phone">Phone</label>
                      <input
                        type="tel"
                        id="phone"
                        name="phone"
                        value={profileData.phone}
                        onChange={handleProfileChange}
                        placeholder="+****************"
                      />
                    </div>

                    <div className="form-group">
                      <label htmlFor="dateOfBirth">Date of Birth</label>
                      <input
                        type="date"
                        id="dateOfBirth"
                        name="dateOfBirth"
                        value={profileData.dateOfBirth}
                        onChange={handleProfileChange}
                      />
                    </div>

                    <div className="form-group">
                      <label htmlFor="location">Location</label>
                      <input
                        type="text"
                        id="location"
                        name="location"
                        value={profileData.location}
                        onChange={handleProfileChange}
                        placeholder="City, Country"
                      />
                    </div>

                    <div className="form-group full-width">
                      <label htmlFor="website">Website</label>
                      <input
                        type="url"
                        id="website"
                        name="website"
                        value={profileData.website}
                        onChange={handleProfileChange}
                        placeholder="https://yourwebsite.com"
                      />
                    </div>

                    <div className="form-group full-width">
                      <label htmlFor="bio">Bio</label>
                      <textarea
                        id="bio"
                        name="bio"
                        value={profileData.bio}
                        onChange={handleProfileChange}
                        rows={4}
                        placeholder="Tell us about yourself..."
                      />
                    </div>
                  </div>

                  <div className="form-actions">
                    <button type="submit" className="btn btn-primary">
                      Save Changes
                    </button>
                    <button
                      type="button"
                      onClick={() => setIsEditing(false)}
                      className="btn btn-secondary"
                    >
                      Cancel
                    </button>
                  </div>
                </form>
              )}
            </div>
          )}

          {activeTab === 'security' && (
            <div className="tab-content">
              <h2>Security Settings</h2>
              
              <form onSubmit={handleSecuritySubmit}>
                <div className="security-section">
                  <h3>Change Password</h3>
                  <div className="form-group">
                    <label htmlFor="currentPassword">Current Password</label>
                    <input
                      type="password"
                      id="currentPassword"
                      name="currentPassword"
                      value={securityData.currentPassword}
                      onChange={handleSecurityChange}
                    />
                  </div>

                  <div className="form-group">
                    <label htmlFor="newPassword">New Password</label>
                    <input
                      type="password"
                      id="newPassword"
                      name="newPassword"
                      value={securityData.newPassword}
                      onChange={handleSecurityChange}
                    />
                  </div>

                  <div className="form-group">
                    <label htmlFor="confirmPassword">Confirm New Password</label>
                    <input
                      type="password"
                      id="confirmPassword"
                      name="confirmPassword"
                      value={securityData.confirmPassword}
                      onChange={handleSecurityChange}
                    />
                  </div>

                  <button type="submit" className="btn btn-primary">
                    Update Password
                  </button>
                </div>
              </form>

              <div className="security-section">
                <h3>Security Options</h3>
                <div className="security-option">
                  <label className="switch-label">
                    <input
                      type="checkbox"
                      name="twoFactorEnabled"
                      checked={securityData.twoFactorEnabled}
                      onChange={handleSecurityChange}
                    />
                    <span className="switch"></span>
                    Enable Two-Factor Authentication
                  </label>
                  <p>Add an extra layer of security to your account</p>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'preferences' && (
            <div className="tab-content">
              <h2>Notification Preferences</h2>
              
              <div className="preferences-section">
                <div className="preference-option">
                  <label className="switch-label">
                    <input
                      type="checkbox"
                      name="emailNotifications"
                      checked={securityData.emailNotifications}
                      onChange={handleSecurityChange}
                    />
                    <span className="switch"></span>
                    Email Notifications
                  </label>
                  <p>Receive updates and notifications via email</p>
                </div>

                <div className="preference-option">
                  <label className="switch-label">
                    <input
                      type="checkbox"
                      name="smsNotifications"
                      checked={securityData.smsNotifications}
                      onChange={handleSecurityChange}
                    />
                    <span className="switch"></span>
                    SMS Notifications
                  </label>
                  <p>Receive important alerts via text message</p>
                </div>
              </div>

              <div className="danger-zone">
                <h3>Danger Zone</h3>
                <button className="btn btn-danger">
                  Delete Account
                </button>
                <p>Once you delete your account, there is no going back. Please be certain.</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
